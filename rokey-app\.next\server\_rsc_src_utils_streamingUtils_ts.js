"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_streamingUtils_ts";
exports.ids = ["_rsc_src_utils_streamingUtils_ts"];
exports.modules = {

/***/ "(rsc)/./src/utils/streamingUtils.ts":
/*!*************************************!*\
  !*** ./src/utils/streamingUtils.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   createFirstTokenTrackingStream: () => (/* binding */ createFirstTokenTrackingStream),\n/* harmony export */   estimateTokenCount: () => (/* binding */ estimateTokenCount),\n/* harmony export */   evaluatePerformance: () => (/* binding */ evaluatePerformance),\n/* harmony export */   getProviderModelFromContext: () => (/* binding */ getProviderModelFromContext),\n/* harmony export */   logStreamingPerformance: () => (/* binding */ logStreamingPerformance)\n/* harmony export */ });\n// Streaming utilities for first token tracking and performance monitoring\nfunction createFirstTokenTrackingStream(originalStream, provider, model) {\n    const reader = originalStream.getReader();\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        async start (controller) {\n            let firstTokenSent = false;\n            const streamStartTime = Date.now();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        controller.close();\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // Check if this chunk contains actual content (first token)\n                    if (!firstTokenSent && chunk.includes(\"delta\")) {\n                        try {\n                            // Parse SSE data to check for content\n                            const lines = chunk.split(\"\\n\");\n                            for (const line of lines){\n                                if (line.startsWith(\"data: \") && !line.includes(\"[DONE]\")) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        const parsed = JSON.parse(jsonData);\n                                        if (parsed.choices?.[0]?.delta?.content) {\n                                            const firstTokenTime = Date.now() - streamStartTime;\n                                            console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model})`);\n                                            firstTokenSent = true;\n                                            break;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors for individual chunks\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                            // Ignore parsing errors, just track timing\n                            if (!firstTokenSent) {\n                                const firstTokenTime = Date.now() - streamStartTime;\n                                console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model}) [fallback detection]`);\n                                firstTokenSent = true;\n                            }\n                        }\n                    }\n                    // Forward the chunk unchanged\n                    controller.enqueue(value);\n                }\n            } catch (error) {\n                console.error(`[${provider} Stream Tracking] Error:`, error);\n                controller.error(error);\n            }\n        }\n    });\n}\n// Enhanced logging for streaming performance\nfunction logStreamingPerformance(provider, model, metrics) {\n    console.log(`📊 STREAMING PERFORMANCE: ${provider}/${model}`);\n    if (metrics.timeToFirstToken !== undefined) {\n        console.log(`   ⏱️ Time to First Token: ${metrics.timeToFirstToken.toFixed(1)}ms`);\n        // Performance categories\n        if (metrics.timeToFirstToken < 500) {\n            console.log(`   ⚡ EXCELLENT first token performance`);\n        } else if (metrics.timeToFirstToken < 1000) {\n            console.log(`   ✅ GOOD first token performance`);\n        } else if (metrics.timeToFirstToken < 2000) {\n            console.log(`   ⚠️ SLOW first token performance`);\n        } else {\n            console.log(`   🐌 VERY SLOW first token performance`);\n        }\n    }\n    if (metrics.totalStreamTime !== undefined) {\n        console.log(`   🔄 Total Stream Time: ${metrics.totalStreamTime.toFixed(1)}ms`);\n    }\n    if (metrics.totalTokens !== undefined) {\n        console.log(`   🎯 Total Tokens: ${metrics.totalTokens}`);\n    }\n    if (metrics.averageTokenLatency !== undefined) {\n        console.log(`   📈 Avg Token Latency: ${metrics.averageTokenLatency.toFixed(1)}ms/token`);\n    }\n}\n// Utility to extract provider and model from request context\nfunction getProviderModelFromContext(providerName, modelId) {\n    return {\n        provider: providerName || \"unknown\",\n        model: modelId || \"unknown\"\n    };\n}\n// Simple token counter for rough estimation\nfunction estimateTokenCount(text) {\n    // Rough estimation: 1 token ≈ 4 characters for English text\n    // This is a simplified approach, real tokenization would be more accurate\n    return Math.ceil(text.length / 4);\n}\n// Performance thresholds for different providers\nconst PERFORMANCE_THRESHOLDS = {\n    EXCELLENT_FIRST_TOKEN: 500,\n    GOOD_FIRST_TOKEN: 1000,\n    SLOW_FIRST_TOKEN: 2000,\n    // Anything above 2000ms is considered very slow\n    EXCELLENT_TOTAL: 3000,\n    GOOD_TOTAL: 5000,\n    SLOW_TOTAL: 10000,\n    TARGET_TOKEN_LATENCY: 50\n};\n// Check if performance meets targets\nfunction evaluatePerformance(metrics) {\n    const firstTokenGrade = !metrics.timeToFirstToken ? \"very_slow\" : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? \"excellent\" : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? \"good\" : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? \"slow\" : \"very_slow\";\n    const totalTimeGrade = !metrics.totalStreamTime ? \"very_slow\" : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? \"excellent\" : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? \"good\" : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? \"slow\" : \"very_slow\";\n    const tokenLatencyGrade = !metrics.averageTokenLatency ? \"very_slow\" : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? \"excellent\" : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? \"good\" : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? \"slow\" : \"very_slow\";\n    // Overall grade is the worst of the three\n    const grades = [\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade\n    ];\n    const gradeOrder = [\n        \"excellent\",\n        \"good\",\n        \"slow\",\n        \"very_slow\"\n    ];\n    const overallGrade = grades.reduce((worst, current)=>{\n        return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;\n    }, \"excellent\");\n    return {\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade,\n        overallGrade\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/streamingUtils.ts\n");

/***/ })

};
;